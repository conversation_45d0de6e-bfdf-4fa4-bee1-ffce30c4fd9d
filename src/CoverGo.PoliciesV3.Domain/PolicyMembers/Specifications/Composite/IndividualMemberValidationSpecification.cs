using System.Collections.Concurrent;
using CoverGo.PoliciesV3.Domain.Common.Constants;
using CoverGo.PoliciesV3.Domain.Common.Extensions;
using CoverGo.PoliciesV3.Domain.Common.Specifications;
using CoverGo.PoliciesV3.Domain.Common.Specifications.Contexts;
using CoverGo.PoliciesV3.Domain.Common.Validation;
using CoverGo.PoliciesV3.Domain.PolicyMemberUploads;
using Microsoft.Extensions.Logging;

namespace CoverGo.PoliciesV3.Domain.PolicyMembers.Specifications.Composite;

public class IndividualMemberValidationSpecification(
    MemberMustHaveUniqueEmailSpecification memberUniqueEmailSpec,
    MemberMustHaveUniqueHKIDSpecification memberUniqueHkidSpec,
    MemberMustHaveUniquePassportSpecification memberUniquePassportSpec,
    MemberMustHaveUniqueStaffNumberSpecification memberUniqueStaffSpec,
    MemberIdMustFollowBusinessRulesSpecification memberIdBusinessRulesSpec,
    MemberFieldsMustMatchSchemaSpecification memberFieldsSchemaSpec,
    MemberEffectiveDateMustBeValidSpecification memberEffectiveDateSpec,
    DependentMustHaveValidPrimaryMemberSpecification dependentValidationSpec,
    MemberMustHaveValidPlanIdSpecification memberValidPlanIdSpec,
    IConcurrentMemberProcessor memberProcessor,
    ILogger<IndividualMemberValidationSpecification> logger) : CompositeSpecificationBase<IndividualMemberValidationContext>
{
    #region Specification Properties

    public override string BusinessRuleName => "Individual Member Validation";
    public override string Description => "Validates business rules that apply to individual members";

    #endregion

    #region Main Validation Logic

    public override async Task<BatchValidationResult> ValidateBatchAsync(
        IndividualMemberValidationContext context,
        CancellationToken cancellationToken = default)
    {
        ArgumentNullException.ThrowIfNull(context);

        if (!context.HasMembersToValidate)
        {
            logger.LogInformation("No members to validate - returning success");
            return BatchValidationResult.Success(0);
        }

        try
        {
            logger.LogInformation("Starting individual member validation for {MemberCount} members", context.MemberCount);

            Dictionary<int, List<ValidationError>> errorDictionary = await ExecuteValidationAsync(
                context, cancellationToken);

            int validCount = context.MemberCount - errorDictionary.Count;
            int invalidCount = errorDictionary.Count;

            logger.LogInformation("Individual member validation completed. Valid: {ValidCount}, Invalid: {InvalidCount}",
                validCount, invalidCount);

            return new BatchValidationResult
            {
                ValidCount = validCount,
                InvalidCount = invalidCount,
                RowErrors = errorDictionary
            };
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error in individual member validation");
            throw;
        }
    }

    #endregion

    #region Protected Virtual Methods

    protected virtual async Task<Dictionary<int, List<ValidationError>>> ExecuteValidationAsync(
        IndividualMemberValidationContext context,
        CancellationToken cancellationToken)
    {
        logger.LogDebug("Starting hybrid validation: parallel non-database operations, sequential database operations");

        // Phase 1: Execute non-database validations in parallel (safe for concurrency)
        Dictionary<int, List<ValidationError>> parallelValidationErrors = await memberProcessor.ProcessMembersAsync(
            context.MemberCount,
            memberIndex => ExecuteNonDatabaseValidationsAsync(context, memberIndex, cancellationToken),
            cancellationToken);

        // Phase 2: Execute database-dependent validations sequentially (prevents DbContext concurrency)
        Dictionary<int, List<ValidationError>> sequentialValidationErrors = await ExecuteUniquenessValidationsSequentiallyAsync(
            context, cancellationToken);

        // Phase 3: Combine results from both validation phases
        Dictionary<int, List<ValidationError>> combinedErrors = CombineValidationResults(
            parallelValidationErrors, sequentialValidationErrors);

        logger.LogDebug("Hybrid validation completed: {ParallelErrors} parallel errors, {SequentialErrors} sequential errors, {TotalErrors} total errors",
            parallelValidationErrors.Count, sequentialValidationErrors.Count, combinedErrors.Count);

        return combinedErrors;
    }

    protected virtual async Task<List<ValidationError>> ExecuteNonDatabaseValidationsAsync(
        IndividualMemberValidationContext context,
        int memberIndex,
        CancellationToken cancellationToken)
    {
        try
        {
            if (memberIndex >= context.MembersFields.Count)
            {
                logger.LogWarning("Member index {MemberIndex} is out of range. Total members: {TotalMembers}",
                    memberIndex, context.MembersFields.Count);
                return [];
            }

            MemberUploadFields memberFields = context.MembersFields[memberIndex];
            List<ValidationError> memberErrors = [];

            await ExecuteSchemaValidation(context, memberFields, memberErrors, cancellationToken);
            await ExecuteEffectiveDateValidation(context, memberFields, memberErrors, cancellationToken);
            await ExecuteMemberIdBusinessRules(context, memberFields, memberErrors, cancellationToken);
            await ExecutePlanIdValidation(context, memberFields, memberErrors, cancellationToken);

            if (memberFields.IsDependent())
            {
                await ExecuteDependentValidation(context, memberFields, memberErrors, cancellationToken);
            }

            return memberErrors;
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error processing non-database validations for member at index {MemberIndex}", memberIndex);
            MemberUploadFields? memberFields = memberIndex < context.MembersFields.Count ? context.MembersFields[memberIndex] : null;
            return [CreateMemberProcessingError(memberIndex, ex, memberFields)];
        }
    }



    protected virtual async Task ExecuteSchemaValidation(
        IndividualMemberValidationContext context,
        MemberUploadFields memberFields,
        List<ValidationError> memberErrors,
        CancellationToken cancellationToken = default)
    {
        var fieldContext = FieldValidationContext.Create(
            memberFields, context.Policy, context.Schema, memberId: null);

        Result result = await memberFieldsSchemaSpec.IsSatisfiedBy(fieldContext, cancellationToken);
        if (result.IsFailure)
        {
            memberErrors.AddRange(result.Errors);
        }
    }

    protected virtual async Task ExecuteEffectiveDateValidation(
        IndividualMemberValidationContext context,
        MemberUploadFields memberFields,
        List<ValidationError> memberErrors,
        CancellationToken cancellationToken = default)
    {
        var fieldContext = FieldValidationContext.Create(
            memberFields, context.Policy, context.Schema, memberId: null);

        Result result = await memberEffectiveDateSpec.IsSatisfiedBy(fieldContext, cancellationToken);
        if (result.IsFailure)
        {
            memberErrors.AddRange(result.Errors);
        }
    }

    protected virtual async Task ExecuteMemberIdBusinessRules(
        IndividualMemberValidationContext context,
        MemberUploadFields memberFields,
        List<ValidationError> memberErrors,
        CancellationToken cancellationToken = default)
    {
        string? memberId = memberFields.Value.TryGetValueOrDefault(PolicyMemberUploadWellKnowFields.MemberIdField);
        if (string.IsNullOrWhiteSpace(memberId)) return;

        ResolvedValidationData resolvedData = context.ResolvedData;
        bool individualExists = resolvedData.IndividualExistenceMap.TryGetValue(memberId, out bool exists) && exists;
        PolicyMember? existingPolicyMember = resolvedData.ExistingPolicyMembers.TryGetValue(memberId, out PolicyMember? member) ? member : null;
        IReadOnlyList<PolicyMember>? memberValidationStates = resolvedData.MemberValidationStates.TryGetValue(memberId, out IReadOnlyList<PolicyMember>? states) ? states : null;

        var memberIdContext = MemberIdValidationContext.Create(
            memberFields, context.Policy, context.Schema,
            [.. resolvedData.ContractHolderPolicyIds],
            individualExists,
            resolvedData.AllowMembersFromOtherContractHolders,
            existingPolicyMember,
            memberValidationStates,
            context.EndorsementId?.Value);

        Result result = await memberIdBusinessRulesSpec.IsSatisfiedBy(memberIdContext, cancellationToken);
        if (result.IsFailure)
        {
            memberErrors.AddRange(result.Errors);
        }

        logger.LogDebug("Member ID business rules validation completed for {MemberId}", memberId);
    }

    protected virtual async Task ExecutePlanIdValidation(
        IndividualMemberValidationContext context,
        MemberUploadFields memberFields,
        List<ValidationError> memberErrors,
        CancellationToken cancellationToken = default)
    {
        IReadOnlySet<string>? availablePlans = context.ResolvedData.AvailablePlans;
        if (availablePlans == null) return;

        var planContext = PlanValidationContext.Create(
            memberFields, context.Policy, context.Schema, availablePlans, context.EndorsementId?.Value);

        Result result = await memberValidPlanIdSpec.IsSatisfiedBy(planContext, cancellationToken);
        if (result.IsFailure)
        {
            memberErrors.AddRange(result.Errors);
        }
    }

    protected virtual async Task ExecuteUniquenessValidations(
        IndividualMemberValidationContext context,
        MemberUploadFields memberFields,
        List<ValidationError> memberErrors,
        CancellationToken cancellationToken)
    {
        ResolvedValidationData resolvedData = context.ResolvedData;
        string? memberId = memberFields.Value.TryGetValueOrDefault(PolicyMemberUploadWellKnowFields.MemberIdField);
        string planId = memberFields.Value.TryGetValueOrDefault(PolicyMemberUploadWellKnowFields.PlanIdField) ?? "DEFAULT";

        UniquenessValidationContext uniquenessContext = UniquenessValidationContext.Builder()
            .WithMemberFields(memberFields)
            .WithPolicy(context.Policy)
            .WithSchema(context.Schema)
            .WithPolicyId(context.Policy.Id)
            .WithPlanId(planId)
            .WithContractHolderId(context.Policy.ContractHolderId?.ToString())
            .WithContractHolderPolicyIds([.. resolvedData.ContractHolderPolicyIds])
            .WithContractHolderValidEndorsementIds([.. resolvedData.ValidEndorsementIds])
            .WithTenantId(resolvedData.TenantId)
            .WithMemberId(memberId)
            .WithContractHolderScopeEndorsements([.. resolvedData.ContractHolderScopeEndorsements])
            .Build();

        // Execute uniqueness validations sequentially to prevent DbContext concurrency issues
        // Each validation calls PolicyMemberUniquenessService which uses DbContext
        Result emailResult = await memberUniqueEmailSpec.IsSatisfiedBy(uniquenessContext, cancellationToken);
        if (emailResult.IsFailure)
        {
            memberErrors.AddRange(emailResult.Errors);
        }

        Result hkidResult = await memberUniqueHkidSpec.IsSatisfiedBy(uniquenessContext, cancellationToken);
        if (hkidResult.IsFailure)
        {
            memberErrors.AddRange(hkidResult.Errors);
        }

        Result passportResult = await memberUniquePassportSpec.IsSatisfiedBy(uniquenessContext, cancellationToken);
        if (passportResult.IsFailure)
        {
            memberErrors.AddRange(passportResult.Errors);
        }

        Result staffResult = await memberUniqueStaffSpec.IsSatisfiedBy(uniquenessContext, cancellationToken);
        if (staffResult.IsFailure)
        {
            memberErrors.AddRange(staffResult.Errors);
        }

        logger.LogDebug("Uniqueness validations completed for member {MemberId}", memberId);
    }

    protected virtual async Task ExecuteDependentValidation(
        IndividualMemberValidationContext context,
        MemberUploadFields memberFields,
        List<ValidationError> memberErrors,
        CancellationToken cancellationToken)
    {
        ResolvedValidationData resolvedData = context.ResolvedData;

        var dependentContext = DependentValidationContext.Create(
            memberFields, context.Policy.Id, context.Policy, context.Schema,
            resolvedData.DependentMembersCache,
            [.. resolvedData.ValidEndorsementIds],
            primaryMemberFields: null,
            context.EndorsementId?.Value);

        Result result = await dependentValidationSpec.IsSatisfiedBy(dependentContext, cancellationToken);
        if (result.IsFailure)
        {
            memberErrors.AddRange(result.Errors);
        }

        logger.LogDebug("Dependent validation completed");
    }

    protected virtual async Task<Dictionary<int, List<ValidationError>>> ExecuteUniquenessValidationsSequentiallyAsync(
        IndividualMemberValidationContext context,
        CancellationToken cancellationToken)
    {
        logger.LogDebug("Starting sequential uniqueness validations for {MemberCount} members", context.MemberCount);

        Dictionary<int, List<ValidationError>> validationErrors = [];

        // Process each member sequentially to avoid DbContext concurrency
        for (int memberIndex = 0; memberIndex < context.MemberCount; memberIndex++)
        {
            try
            {
                if (memberIndex >= context.MembersFields.Count)
                {
                    logger.LogWarning("Member index {MemberIndex} is out of range during uniqueness validation. Total members: {TotalMembers}",
                        memberIndex, context.MembersFields.Count);
                    continue;
                }

                MemberUploadFields memberFields = context.MembersFields[memberIndex];
                List<ValidationError> memberErrors = [];

                // Execute uniqueness validations sequentially (these use DbContext)
                await ExecuteUniquenessValidations(context, memberFields, memberErrors, cancellationToken);

                if (memberErrors.Count > 0)
                {
                    validationErrors[memberIndex] = memberErrors;
                }
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Error processing uniqueness validations for member at index {MemberIndex}", memberIndex);
                MemberUploadFields? memberFields = memberIndex < context.MembersFields.Count ? context.MembersFields[memberIndex] : null;
                validationErrors[memberIndex] = [CreateMemberProcessingError(memberIndex, ex, memberFields)];
            }
        }

        logger.LogDebug("Sequential uniqueness validations completed: {ErrorCount} members with uniqueness errors", validationErrors.Count);
        return validationErrors;
    }

    protected virtual Dictionary<int, List<ValidationError>> CombineValidationResults(
        Dictionary<int, List<ValidationError>> parallelValidationErrors,
        Dictionary<int, List<ValidationError>> sequentialValidationErrors)
    {
        Dictionary<int, List<ValidationError>> combinedErrors = [];

        // Get all member indices that have errors from either validation phase
        HashSet<int> allErrorIndices = [.. parallelValidationErrors.Keys, .. sequentialValidationErrors.Keys];

        foreach (int memberIndex in allErrorIndices)
        {
            List<ValidationError> memberErrors = [];

            // Add parallel validation errors if they exist
            if (parallelValidationErrors.TryGetValue(memberIndex, out List<ValidationError>? parallelErrors))
            {
                memberErrors.AddRange(parallelErrors);
            }

            // Add sequential validation errors if they exist
            if (sequentialValidationErrors.TryGetValue(memberIndex, out List<ValidationError>? sequentialErrors))
            {
                memberErrors.AddRange(sequentialErrors);
            }

            if (memberErrors.Count > 0)
            {
                combinedErrors[memberIndex] = memberErrors;
            }
        }

        return combinedErrors;
    }

    #endregion

    #region Private Helper Methods

    private static ValidationError CreateMemberProcessingError(int memberIndex, Exception ex, MemberUploadFields? memberFields)
    {
        string? memberId = memberFields?.Value.TryGetValueOrDefault(PolicyMemberUploadWellKnowFields.MemberIdField);

        return Errors.MemberProcessingFailed(
            "member",
            memberIndex + 1,
            memberId,
            ex.Message,
            "Member");
    }

    #endregion
}

#region Supporting Interface and Implementation

public interface IConcurrentMemberProcessor
{
    Task<Dictionary<int, List<ValidationError>>> ProcessMembersAsync(
        int memberCount,
        Func<int, Task<List<ValidationError>>> memberValidator,
        CancellationToken cancellationToken);
}

public class ConcurrentMemberProcessor(
    int maxConcurrentValidations = ValidationConstants.Concurrency.DefaultMaxConcurrentValidations) : IConcurrentMemberProcessor
{
    private readonly int _maxConcurrentValidations = maxConcurrentValidations;

    public async Task<Dictionary<int, List<ValidationError>>> ProcessMembersAsync(
        int memberCount,
        Func<int, Task<List<ValidationError>>> memberValidator,
        CancellationToken cancellationToken)
    {
        ConcurrentDictionary<int, List<ValidationError>> concurrentErrors = [];

        using var semaphore = new SemaphoreSlim(_maxConcurrentValidations, _maxConcurrentValidations);

        OrderablePartitioner<Tuple<int, int>> partitioner = Partitioner.Create(0, memberCount);
        IEnumerable<Task> tasks = partitioner.GetDynamicPartitions().Select(range =>
            ProcessRangeAsync(range, semaphore, memberValidator, concurrentErrors, cancellationToken));

        await Task.WhenAll(tasks);

        return new Dictionary<int, List<ValidationError>>(concurrentErrors);
    }

    private static async Task ProcessRangeAsync(
        Tuple<int, int> range,
        SemaphoreSlim semaphore,
        Func<int, Task<List<ValidationError>>> memberValidator,
        ConcurrentDictionary<int, List<ValidationError>> concurrentErrors,
        CancellationToken cancellationToken)
    {
        await semaphore.WaitAsync(cancellationToken);
        try
        {
            for (int i = range.Item1; i < range.Item2; i++)
            {
                List<ValidationError> memberErrors = await memberValidator(i);
                if (memberErrors.Count > 0)
                {
                    concurrentErrors[i] = memberErrors;
                }
            }
        }
        finally
        {
            semaphore.Release();
        }
    }
}

#endregion