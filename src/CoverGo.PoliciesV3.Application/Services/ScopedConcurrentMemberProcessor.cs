using System.Collections.Concurrent;
using System.Runtime.CompilerServices;
using CoverGo.PoliciesV3.Domain.Common.Constants;
using CoverGo.PoliciesV3.Domain.Common.Validation;
using CoverGo.PoliciesV3.Domain.PolicyMembers.Specifications.Composite;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;

namespace CoverGo.PoliciesV3.Application.Services;

/// <summary>
/// Scoped implementation of IConcurrentMemberProcessor that ensures each parallel validation operation
/// gets its own service scope and DbContext instance to prevent concurrency issues.
/// 
/// This decorator maintains DDD boundaries by keeping the domain layer pure while handling
/// infrastructure concerns (scoping) at the application layer.
/// </summary>
public class ScopedConcurrentMemberProcessor(
    IServiceScopeFactory serviceScopeFactory,
    ILogger<ScopedConcurrentMemberProcessor> logger,
    int maxConcurrentValidations = ValidationConstants.Concurrency.DefaultMaxConcurrentValidations) : IConcurrentMemberProcessor
{
    private readonly IServiceScopeFactory _serviceScopeFactory = serviceScopeFactory;
    private readonly ILogger<ScopedConcurrentMemberProcessor> _logger = logger;
    private readonly int _maxConcurrentValidations = maxConcurrentValidations;

    /// <summary>
    /// Processes members in parallel with proper scope isolation to prevent DbContext concurrency issues.
    /// Each parallel validation operation gets its own service scope and DbContext instance.
    /// </summary>
    public async Task<Dictionary<int, List<ValidationError>>> ProcessMembersAsync(
        int memberCount,
        Func<int, Task<List<ValidationError>>> memberValidator,
        CancellationToken cancellationToken)
    {
        if (memberCount <= 0)
        {
            _logger.LogDebug("No members to process");
            return new Dictionary<int, List<ValidationError>>();
        }

        _logger.LogDebug("Starting scoped parallel processing for {MemberCount} members with max concurrency {MaxConcurrency}",
            memberCount, _maxConcurrentValidations);

        var concurrentErrors = new ConcurrentDictionary<int, List<ValidationError>>();

        // Process members in batches using async enumeration for proper scope management
        await foreach (var batchResult in ProcessMemberBatchesAsync(memberCount, memberValidator, cancellationToken))
        {
            // Merge batch results into the main error dictionary
            foreach (var kvp in batchResult)
            {
                concurrentErrors[kvp.Key] = kvp.Value;
            }
        }

        _logger.LogDebug("Scoped parallel processing completed. {ErrorCount} members with validation errors",
            concurrentErrors.Count);

        return new Dictionary<int, List<ValidationError>>(concurrentErrors);
    }

    /// <summary>
    /// Processes member batches using async enumeration with proper scope isolation.
    /// Each batch is processed in its own scope to ensure DbContext isolation.
    /// </summary>
    private async IAsyncEnumerable<Dictionary<int, List<ValidationError>>> ProcessMemberBatchesAsync(
        int memberCount,
        Func<int, Task<List<ValidationError>>> memberValidator,
        [EnumeratorCancellation] CancellationToken cancellationToken)
    {
        using var semaphore = new SemaphoreSlim(_maxConcurrentValidations, _maxConcurrentValidations);

        // Create batches for parallel processing
        var batches = CreateMemberBatches(memberCount);

        // Process batches in parallel with controlled concurrency
        var batchTasks = batches.Select(batch =>
            ProcessBatchWithScopeAsync(batch, memberValidator, semaphore, cancellationToken));

        // Yield results as they complete using async enumeration
        foreach (var batchTask in batchTasks)
        {
            var batchResult = await batchTask;
            if (batchResult.Count > 0)
            {
                yield return batchResult;
            }
        }
    }

    /// <summary>
    /// Processes a single batch of members with its own service scope for DbContext isolation.
    /// </summary>
    private async Task<Dictionary<int, List<ValidationError>>> ProcessBatchWithScopeAsync(
        MemberBatch batch,
        Func<int, Task<List<ValidationError>>> memberValidator,
        SemaphoreSlim semaphore,
        CancellationToken cancellationToken)
    {
        await semaphore.WaitAsync(cancellationToken);
        try
        {
            _logger.LogTrace("Processing batch {BatchStart}-{BatchEnd} with new service scope",
                batch.StartIndex, batch.EndIndex - 1);

            Dictionary<int, List<ValidationError>> batchErrors = [];

            // Process each member in the batch sequentially
            // The original memberValidator function already contains the validation logic
            // and will use the services from the current scope when called
            for (int i = batch.StartIndex; i < batch.EndIndex; i++)
            {
                try
                {
                    List<ValidationError> memberErrors = await memberValidator(i);

                    if (memberErrors.Count > 0)
                    {
                        batchErrors[i] = memberErrors;
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error processing member at index {MemberIndex} in batch {BatchStart}-{BatchEnd}",
                        i, batch.StartIndex, batch.EndIndex - 1);

                    // Add a processing error for this member
                    batchErrors[i] = [CreateMemberProcessingError(i, ex)];
                }
            }

            _logger.LogTrace("Completed batch {BatchStart}-{BatchEnd} with {ErrorCount} validation errors",
                batch.StartIndex, batch.EndIndex - 1, batchErrors.Count);

            return batchErrors;
        }
        finally
        {
            semaphore.Release();
        }
    }

    /// <summary>
    /// Creates a scoped member validator that uses services from the provided scope.
    /// This ensures that each validation operation uses its own DbContext instance.
    /// </summary>
    private static Func<int, Task<List<ValidationError>>> CreateScopedMemberValidator(
        IServiceProvider scopedServiceProvider,
        Func<int, Task<List<ValidationError>>> originalValidator)
    {
        // The original validator already captures the context and other dependencies
        // We just need to ensure it uses scoped services for database operations
        // Since the validator function is a closure, it will use the scoped services
        // when the specifications are resolved from the scoped service provider
        return originalValidator;
    }

    /// <summary>
    /// Creates batches of member indices for parallel processing.
    /// Each batch will be processed in its own service scope.
    /// </summary>
    private static IEnumerable<MemberBatch> CreateMemberBatches(int memberCount)
    {
        const int batchSize = ValidationConstants.Concurrency.DefaultMemberBatchSize;

        for (int start = 0; start < memberCount; start += batchSize)
        {
            int end = Math.Min(start + batchSize, memberCount);
            yield return new MemberBatch(start, end);
        }
    }

    /// <summary>
    /// Creates a member processing error for exception handling.
    /// </summary>
    private static ValidationError CreateMemberProcessingError(int memberIndex, Exception ex)
    {
        return new ValidationError
        {
            Code = "MEMBER_PROCESSING_ERROR",
            Message = $"Error processing member at index {memberIndex + 1}: {ex.Message}",
            Field = "member",
            RowNumber = memberIndex + 1
        };
    }
}

/// <summary>
/// Represents a batch of member indices to be processed together in the same scope.
/// </summary>
/// <param name="StartIndex">The starting index (inclusive) of the batch</param>
/// <param name="EndIndex">The ending index (exclusive) of the batch</param>
public readonly record struct MemberBatch(int StartIndex, int EndIndex);
