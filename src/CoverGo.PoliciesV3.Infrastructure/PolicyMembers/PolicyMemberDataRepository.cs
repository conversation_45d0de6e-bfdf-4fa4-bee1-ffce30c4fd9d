using CoverGo.BuildingBlocks.Application.Core.Ports;
using CoverGo.BuildingBlocks.DataAccess.PostgreSql;
using CoverGo.BuildingBlocks.DataAccess.PostgreSql.Constants;
using CoverGo.PoliciesV3.Domain.Policies;
using CoverGo.PoliciesV3.Domain.PolicyMembers;
using CoverGo.PoliciesV3.Infrastructure.DataAccess;
using CoverGo.PoliciesV3.Infrastructure.DataAccess.Extensions;
using Microsoft.EntityFrameworkCore;

namespace CoverGo.PoliciesV3.Infrastructure.PolicyMembers;

/// <summary>
/// Infrastructure implementation of domain repository for basic policy member data access
/// Contains only data retrieval operations without business logic
/// Uses DbContextFactory for parallel operations to prevent concurrency issues
/// </summary>
internal sealed class PolicyMemberDataRepository(
    PostgreSqlUnitOfWork<ApplicationDbContext> unitOfWork,
    IUserContextProvider userContextProvider,
    IDbContextFactory<ApplicationDbContext> dbContextFactory)
    : PostgreSqlRepository<PolicyMember, PolicyMemberId, ApplicationDbContext>(unitOfWork, userContextProvider), IPolicyMemberDataRepository
{
    public async Task<List<PolicyMember>> GetByExcludingPolicyAsync(
        PolicyId excludePolicyId,
        CancellationToken cancellationToken = default)
    {
        // Use DbContextFactory to create a new context for parallel operations
        using var context = await dbContextFactory.CreateDbContextAsync(cancellationToken);
        return await context.PolicyMembers
            .Where(pm => pm.PolicyId != excludePolicyId)
            .WhereNotRemoved()
            .Include(pm => pm.States)
            .AsNoTracking()
            .ToListAsync(cancellationToken);
    }

    public async Task<List<PolicyMember>> GetByPolicyIdAsync(
        PolicyId policyId,
        CancellationToken cancellationToken = default)
    {
        // Use DbContextFactory to create a new context for parallel operations
        using var context = await dbContextFactory.CreateDbContextAsync(cancellationToken);
        return await context.PolicyMembers
            .Where(pm => pm.PolicyId == policyId)
            .WhereNotRemoved()
            .Include(pm => pm.States)
            .AsNoTracking()
            .ToListAsync(cancellationToken);
    }

    public async Task<List<PolicyMember>> GetByPolicyIdsAsync(
        List<PolicyId> policyIds,
        CancellationToken cancellationToken = default)
    {
        if (policyIds.Count == 0)
            return [];

        if (policyIds.Count > ConfigurationConstants.RepositoryBatchSizes.PolicyIdChunkingThreshold)
            return await GetByPolicyIdsChunked(policyIds, cancellationToken);

        // Use DbContextFactory to create a new context for parallel operations
        using var context = await dbContextFactory.CreateDbContextAsync(cancellationToken);
        return await context.PolicyMembers
            .Where(pm => policyIds.Contains(pm.PolicyId))
            .WhereNotRemoved()
            .Include(pm => pm.States)
            .AsNoTracking()
            .ToListAsync(cancellationToken);
    }

    public async Task<List<PolicyMember>> GetByMemberIdAsync(
        string memberId,
        CancellationToken cancellationToken = default) => string.IsNullOrWhiteSpace(memberId)
            ? []
            : await Entity
            .Where(pm => pm.MemberId == memberId)
            .WhereNotRemoved()
            .Include(pm => pm.States)
            .AsNoTracking()
            .ToListAsync(cancellationToken);

    public async Task<List<PolicyMember>> GetByMemberIdsAndPolicyAsync(
        List<string> memberIds,
        PolicyId policyId,
        CancellationToken cancellationToken = default) => memberIds.Count == 0
            ? []
            : await Entity
            .Where(pm => memberIds.Contains(pm.MemberId) && pm.PolicyId == policyId)
            .WhereNotRemoved()
            .Include(pm => pm.States)
            .AsNoTracking()
            .ToListAsync(cancellationToken);

    /// <summary>
    /// Gets policy members by member IDs with batch optimization
    /// </summary>
    public async Task<List<PolicyMember>> GetByMemberIdsAsync(
        IReadOnlyList<string> memberIds,
        CancellationToken cancellationToken = default)
    {
        if (memberIds.Count == 0)
            return [];

        return memberIds.Count > ConfigurationConstants.RepositoryBatchSizes.MemberIdChunkingThreshold
            ? await GetByMemberIdsChunked(memberIds, cancellationToken)
            : await Entity
            .Where(pm => memberIds.Contains(pm.MemberId))
            .WhereNotRemoved()
            .Include(pm => pm.States)
            .AsNoTracking()
            .ToListAsync(cancellationToken);
    }

    #region Private Helper Methods

    private async Task<List<PolicyMember>> GetByPolicyIdsChunked(
        List<PolicyId> policyIds,
        CancellationToken cancellationToken)
    {
        const int chunkSize = ConfigurationConstants.RepositoryBatchSizes.PolicyIdChunkSize;

        IEnumerable<Task<List<PolicyMember>>> chunkTasks = policyIds
            .Chunk(chunkSize)
            .Select(async chunk =>
            {
                // Use DbContextFactory for each chunk to prevent concurrency issues
                using var context = await dbContextFactory.CreateDbContextAsync(cancellationToken);
                return await context.PolicyMembers
                    .Where(pm => chunk.Contains(pm.PolicyId))
                    .WhereNotRemoved()
                    .Include(pm => pm.States)
                    .AsNoTracking()
                    .ToListAsync(cancellationToken);
            });

        List<PolicyMember>[] chunkResults = await Task.WhenAll(chunkTasks);
        return [.. chunkResults.SelectMany(result => result)];
    }

    private async Task<List<PolicyMember>> GetByMemberIdsChunked(
        IReadOnlyList<string> memberIds,
        CancellationToken cancellationToken)
    {
        const int chunkSize = ConfigurationConstants.RepositoryBatchSizes.MemberIdChunkSize;

        IEnumerable<Task<List<PolicyMember>>> chunkTasks = memberIds
            .Chunk(chunkSize)
            .Select(async chunk =>
            {
                // Use DbContextFactory for each chunk to prevent concurrency issues
                using var context = await dbContextFactory.CreateDbContextAsync(cancellationToken);
                return await context.PolicyMembers
                    .Where(pm => chunk.Contains(pm.MemberId))
                    .WhereNotRemoved()
                    .Include(pm => pm.States)
                    .AsNoTracking()
                    .ToListAsync(cancellationToken);
            });

        List<PolicyMember>[] chunkResults = await Task.WhenAll(chunkTasks);
        return [.. chunkResults.SelectMany(result => result)];
    }

    #endregion
}

